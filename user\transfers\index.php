<?php
/**
 * User Transfer Page
 * Comprehensive money transfer system with three transfer types
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once '../../config/config.php';
require_once '../../config/dynamic-css.php';

// Get user data from database
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user account information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

if (!$user) {
    header('Location: ../../auth/login.php');
    exit();
}

// Get user's beneficiaries for quick selection
$beneficiaries_query = "SELECT b.*, 
                               a.id as internal_user_id,
                               a.first_name as internal_first_name,
                               a.last_name as internal_last_name
                        FROM beneficiaries b
                        LEFT JOIN accounts a ON b.account_number = a.account_number AND a.is_admin = 0
                        WHERE b.user_id = ? 
                        ORDER BY b.is_favorite DESC, b.name ASC";
$beneficiaries_result = $db->query($beneficiaries_query, [$user_id]);
$beneficiaries = [];
while ($row = $beneficiaries_result->fetch_assoc()) {
    $beneficiaries[] = $row;
}

// Get virtual card balance if available
$virtual_card_balance = 0;
try {
    $virtual_card_query = "SELECT COALESCE(SUM(spending_limit), 0) as total_balance FROM virtual_cards WHERE user_id = ? AND status = 'active'";
    $virtual_card_result = $db->query($virtual_card_query, [$user_id]);
    $virtual_card_balance = $virtual_card_result->fetch_assoc()['total_balance'];
} catch (Exception $e) {
    $virtual_card_balance = 0;
}

// Check if a beneficiary is pre-selected
$selected_beneficiary = null;
if (isset($_GET['beneficiary'])) {
    $beneficiary_id = intval($_GET['beneficiary']);
    foreach ($beneficiaries as $beneficiary) {
        if ($beneficiary['id'] == $beneficiary_id) {
            $selected_beneficiary = $beneficiary;
            break;
        }
    }
}

// Set page title and subtitle
$page_title = 'Money Transfer';
$page_subtitle = 'Send money quickly and securely';

// Include header
require_once '../shared/header.php';
?>

<!-- Include Transfer CSS -->
<link rel="stylesheet" href="transfers.css">

<!-- Dynamic CSS Variables Only -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content" style="min-height: calc(100vh - 200px); display: flex; flex-direction: column;">

        <!-- Content Container -->
        <div class="content-container" style="flex: 1;">

        <!-- Mini Hero Section -->
        <div class="transfer-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title">Money Transfer</div>
                    <div class="hero-subtitle">Send money to anyone, anywhere with our secure transfer system</div>
                    <div class="hero-stats">
                        Available Balance: <?php echo formatCurrency($user['balance'], $user['currency']); ?>
                        <?php if ($virtual_card_balance > 0): ?>
                        | Virtual Cards: <?php echo formatCurrency($virtual_card_balance, $user['currency']); ?>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="../beneficiaries/" class="btn btn-outline-light">
                        <i class="fas fa-users me-2"></i>Manage Beneficiaries
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Beneficiary Selection (Always Visible) -->
        <?php if (!empty($beneficiaries)): ?>
        <div class="quick-beneficiaries-section mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-users me-2"></i>Quick Transfer to Saved Beneficiaries</h5>
                </div>
                <div class="card-body">
                    <div class="beneficiaries-quick-grid">
                        <?php foreach (array_slice($beneficiaries, 0, 8) as $beneficiary): ?>
                        <div class="beneficiary-quick-card"
                             onclick="quickTransferToBeneficiary(<?php echo htmlspecialchars(json_encode($beneficiary), ENT_QUOTES); ?>)">
                            <div class="beneficiary-avatar">
                                <?php echo strtoupper(substr($beneficiary['name'], 0, 2)); ?>
                            </div>
                            <div class="beneficiary-info">
                                <div class="beneficiary-name"><?php echo htmlspecialchars($beneficiary['name']); ?></div>
                                <div class="beneficiary-account">****<?php echo substr($beneficiary['account_number'], -4); ?></div>
                                <?php if ($beneficiary['internal_user_id']): ?>
                                <div class="internal-badge">
                                    <i class="fas fa-university"></i> Internal
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="quick-transfer-btn">
                                <i class="fas fa-paper-plane"></i>
                            </div>
                        </div>
                        <?php endforeach; ?>

                        <?php if (count($beneficiaries) > 8): ?>
                        <div class="beneficiary-quick-card view-all" onclick="window.location.href='../beneficiaries/'">
                            <div class="beneficiary-avatar">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="beneficiary-info">
                                <div class="beneficiary-name">View All</div>
                                <div class="beneficiary-account"><?php echo count($beneficiaries) - 8; ?> more</div>
                            </div>
                            <div class="quick-transfer-btn">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Transfer Type Selection -->
        <div class="transfer-types-section mb-4">
            <div class="transfer-types-header">
                <h3><i class="fas fa-paper-plane me-2"></i>Choose Transfer Type</h3>
                <p class="text-muted">Select the type of transfer you want to make, or use quick transfer above</p>
            </div>
            
            <div class="transfer-types-grid">
                <!-- Inter-Bank Transfer -->
                <div class="transfer-type-card" data-type="inter-bank">
                    <div class="transfer-type-icon">
                        <i class="fas fa-university"></i>
                    </div>
                    <div class="transfer-type-info">
                        <h5>Inter-Bank Transfer</h5>
                        <p>Transfer to other users within our banking system</p>
                        <div class="transfer-type-features">
                            <span class="feature-badge">Instant</span>
                            <span class="feature-badge">No Fees</span>
                            <span class="feature-badge">24/7</span>
                        </div>
                    </div>
                    <div class="transfer-type-action">
                        <button class="btn btn-primary" onclick="selectTransferType('inter-bank')">
                            Select
                        </button>
                    </div>
                </div>

                <!-- Local Bank Transfer -->
                <div class="transfer-type-card" data-type="local-bank">
                    <div class="transfer-type-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="transfer-type-info">
                        <h5>Local Bank Transfer</h5>
                        <p>Transfer to external bank accounts in your country</p>
                        <div class="transfer-type-features">
                            <span class="feature-badge">1-3 Days</span>
                            <span class="feature-badge">Low Fees</span>
                            <span class="feature-badge">OTP Secure</span>
                        </div>
                    </div>
                    <div class="transfer-type-action">
                        <button class="btn btn-primary" onclick="selectTransferType('local-bank')">
                            Select
                        </button>
                    </div>
                </div>

                <!-- Wire/International Transfer -->
                <div class="transfer-type-card disabled" data-type="wire-international">
                    <div class="transfer-type-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="transfer-type-info">
                        <h5>Wire/International Transfer</h5>
                        <p>Send money internationally via SWIFT network</p>
                        <div class="transfer-type-features">
                            <span class="feature-badge">Coming Soon</span>
                        </div>
                    </div>
                    <div class="transfer-type-action">
                        <button class="btn btn-secondary" disabled>
                            Coming Soon
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transfer Form Section (Initially Hidden) -->
        <div id="transferFormSection" class="transfer-form-section" style="display: none;">
            <div class="transfer-form-header">
                <h3 id="transferFormTitle"><i class="fas fa-edit me-2"></i>Transfer Details</h3>
                <button class="btn btn-outline-secondary btn-sm" onclick="resetTransferForm()">
                    <i class="fas fa-arrow-left me-2"></i>Back to Types
                </button>
            </div>

            <form id="transferForm" class="transfer-form">
                <input type="hidden" id="transferType" name="transfer_type">
                
                <!-- Source Account Selection -->
                <div class="form-section">
                    <h5><i class="fas fa-wallet me-2"></i>From Account</h5>
                    <div class="source-accounts">
                        <div class="source-account-option">
                            <input type="radio" name="source_account" value="main" id="source_main" checked>
                            <label for="source_main" class="source-account-label">
                                <div class="account-info">
                                    <div class="account-name">Main Account</div>
                                    <div class="account-number"><?php echo htmlspecialchars($user['account_number']); ?></div>
                                </div>
                                <div class="account-balance">
                                    <div class="balance-label">Available Balance</div>
                                    <div class="balance-amount"><?php echo formatCurrency($user['balance'], $user['currency']); ?></div>
                                </div>
                            </label>
                        </div>
                        
                        <?php if ($virtual_card_balance > 0): ?>
                        <div class="source-account-option">
                            <input type="radio" name="source_account" value="virtual_card" id="source_virtual">
                            <label for="source_virtual" class="source-account-label">
                                <div class="account-info">
                                    <div class="account-name">Virtual Card Wallet</div>
                                    <div class="account-number">Virtual Cards Balance</div>
                                </div>
                                <div class="account-balance">
                                    <div class="balance-label">Available Balance</div>
                                    <div class="balance-amount"><?php echo formatCurrency($virtual_card_balance, $user['currency']); ?></div>
                                </div>
                            </label>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Beneficiary Selection/Details -->
                <div class="form-section">
                    <h5><i class="fas fa-user me-2"></i>To Beneficiary</h5>

                    <!-- Quick Beneficiary Selection -->
                    <?php if (!empty($beneficiaries)): ?>
                    <div class="quick-beneficiaries mb-3">
                        <label class="form-label">Quick Select from Saved Beneficiaries</label>
                        <div class="beneficiaries-list">
                            <?php foreach (array_slice($beneficiaries, 0, 6) as $beneficiary): ?>
                            <div class="beneficiary-quick-option"
                                 onclick="selectBeneficiary(<?php echo htmlspecialchars(json_encode($beneficiary), ENT_QUOTES); ?>)">
                                <div class="beneficiary-avatar">
                                    <?php echo strtoupper(substr($beneficiary['name'], 0, 2)); ?>
                                </div>
                                <div class="beneficiary-info">
                                    <div class="beneficiary-name"><?php echo htmlspecialchars($beneficiary['name']); ?></div>
                                    <div class="beneficiary-account">****<?php echo substr($beneficiary['account_number'], -4); ?></div>
                                </div>
                                <?php if ($beneficiary['internal_user_id']): ?>
                                <div class="internal-indicator" title="Internal User">
                                    <i class="fas fa-university"></i>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; ?>

                            <?php if (count($beneficiaries) > 6): ?>
                            <div class="beneficiary-quick-option view-all" onclick="showAllBeneficiaries()">
                                <div class="beneficiary-avatar">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div class="beneficiary-info">
                                    <div class="beneficiary-name">View All</div>
                                    <div class="beneficiary-account"><?php echo count($beneficiaries) - 6; ?> more</div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Manual Beneficiary Details -->
                    <div class="beneficiary-details">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Beneficiary Account Number <span class="text-danger">*</span></label>
                                    <input type="text" name="beneficiary_account" class="form-control" required
                                           placeholder="Enter account number" id="beneficiaryAccount"
                                           <?php if ($selected_beneficiary): ?>
                                           value="<?php echo htmlspecialchars($selected_beneficiary['account_number']); ?>"
                                           <?php endif; ?>>
                                    <div class="form-text">Account number of the recipient</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Beneficiary Account Name <span class="text-danger">*</span></label>
                                    <input type="text" name="beneficiary_name" class="form-control" required
                                           placeholder="Enter account holder name" id="beneficiaryName"
                                           <?php if ($selected_beneficiary): ?>
                                           value="<?php echo htmlspecialchars($selected_beneficiary['name']); ?>"
                                           <?php endif; ?>>
                                    <div class="form-text">Full name of the account holder</div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional fields for Local Bank Transfer -->
                        <div id="localBankFields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Beneficiary Bank Name <span class="text-danger">*</span></label>
                                        <input type="text" name="beneficiary_bank" class="form-control"
                                               placeholder="Enter bank name" id="beneficiaryBank"
                                               <?php if ($selected_beneficiary): ?>
                                               value="<?php echo htmlspecialchars($selected_beneficiary['bank_name']); ?>"
                                               <?php endif; ?>>
                                        <div class="form-text">Name of the recipient's bank</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Routing Code/IBAN/IFSC</label>
                                        <input type="text" name="routing_code" class="form-control"
                                               placeholder="Enter routing code" id="routingCode"
                                               <?php if ($selected_beneficiary): ?>
                                               value="<?php echo htmlspecialchars($selected_beneficiary['bank_code']); ?>"
                                               <?php endif; ?>>
                                        <div class="form-text">Bank routing or identification code</div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Account Type</label>
                                        <select name="account_type" class="form-control" id="accountType">
                                            <option value="">Select Account Type</option>
                                            <option value="savings">Savings Account</option>
                                            <option value="checking">Checking Account</option>
                                            <option value="business">Business Account</option>
                                            <option value="current">Current Account</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transfer Amount -->
                <div class="form-section">
                    <h5><i class="fas fa-dollar-sign me-2"></i>Transfer Amount</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Amount <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text"><?php echo $user['currency']; ?></span>
                                    <input type="number" name="amount" class="form-control" required
                                           step="0.01" min="1" placeholder="0.00" id="transferAmount">
                                </div>
                                <div class="form-text">Minimum transfer: <?php echo formatCurrency(1, $user['currency']); ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Narration/Purpose</label>
                                <input type="text" name="narration" class="form-control"
                                       placeholder="Funds description" id="transferNarration">
                                <div class="form-text">Brief description of the transfer purpose</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transfer Summary -->
                <div class="form-section">
                    <div class="transfer-summary">
                        <h5><i class="fas fa-receipt me-2"></i>Transfer Summary</h5>
                        <div class="summary-details">
                            <div class="summary-row">
                                <span class="summary-label">Transfer Amount:</span>
                                <span class="summary-value" id="summaryAmount">-</span>
                            </div>
                            <div class="summary-row">
                                <span class="summary-label">Transfer Fee:</span>
                                <span class="summary-value" id="summaryFee">Free</span>
                            </div>
                            <div class="summary-row total">
                                <span class="summary-label">Total Debit:</span>
                                <span class="summary-value" id="summaryTotal">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="resetTransferForm()">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-primary" id="submitTransfer">
                        <i class="fas fa-paper-plane me-2"></i>Proceed to Transfer
                    </button>
                </div>
            </form>
        </div>

        </div> <!-- End Content Container -->

    </div>

    <!-- OTP Verification Modal -->
    <div class="modal fade" id="otpModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-shield-alt me-2"></i>Verify Transfer
                    </h5>
                </div>
                <div class="modal-body text-center">
                    <div class="otp-verification">
                        <div class="otp-icon mb-3">
                            <i class="fas fa-mobile-alt fa-3x text-primary"></i>
                        </div>
                        <h6>Enter Verification Code</h6>
                        <p class="text-muted mb-4">
                            We've sent a verification code to your registered email address.
                            Please enter the code below to complete your transfer.
                        </p>
                        <div class="otp-input-group mb-3">
                            <input type="text" class="form-control text-center" id="otpCode"
                                   placeholder="Enter 6-digit code" maxlength="6" style="font-size: 1.2rem; letter-spacing: 0.5rem;">
                        </div>
                        <div class="otp-actions">
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="resendOTP()">
                                <i class="fas fa-redo me-1"></i>Resend Code
                            </button>
                            <button type="button" class="btn btn-primary" onclick="verifyOTPAndTransfer()">
                                <i class="fas fa-check me-1"></i>Verify & Transfer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfer Success Modal -->
    <div class="modal fade" id="successModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center p-4">
                    <div class="success-animation mb-3">
                        <i class="fas fa-check-circle fa-4x text-success"></i>
                    </div>
                    <h4 class="text-success mb-3">Transfer Successful!</h4>
                    <p class="text-muted mb-3" id="successMessage">
                        Your transfer has been processed successfully.
                    </p>

                    <!-- Receipt Email Status -->
                    <div id="receiptEmailStatus" class="alert mb-3" style="display: none;">
                        <!-- Status will be updated by JavaScript -->
                    </div>

                    <!-- Receipt Options -->
                    <div class="receipt-options mb-4">
                        <h6 class="text-muted mb-3">📄 Receipt Options</h6>
                        <div class="d-flex justify-content-center gap-2 flex-wrap">
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="downloadReceipt()">
                                <i class="fas fa-file-pdf me-1"></i>Download PDF
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="viewReceipt()">
                                <i class="fas fa-eye me-1"></i>View Receipt
                            </button>
                        </div>
                    </div>

                    <div class="success-actions">
                        <button type="button" class="btn btn-primary" onclick="closeSuccessModal()">
                            <i class="fas fa-check me-1"></i>Done
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Footer Component -->
    <?php require_once '../shared/user_footer.php'; ?>
</div>

<!-- Include Transfer JavaScript -->
<script src="transfers.js"></script>

<!-- Pre-select beneficiary if provided -->
<?php if ($selected_beneficiary): ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-select the beneficiary and show appropriate transfer type
    const beneficiary = <?php echo json_encode($selected_beneficiary); ?>;

    // Determine transfer type based on whether it's internal
    const transferType = beneficiary.internal_user_id ? 'inter-bank' : 'local-bank';

    // Select the transfer type and populate form
    setTimeout(() => {
        selectTransferType(transferType);
        selectBeneficiary(beneficiary);
    }, 500);
});
</script>
<?php endif; ?>
