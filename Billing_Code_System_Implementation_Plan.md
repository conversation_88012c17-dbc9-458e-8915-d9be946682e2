# Billing Code System Implementation Plan & Progress

## 🎯 **Project Overview**

### **Core Concept**
A flexible per-user billing code verification system for wire transfers that enhances security without disrupting the existing OTP-only system.

### **Simple Logic**
- **User has NO billing codes assigned:** Transfer form → OTP verification → Transfer processed (current system unchanged)
- **User has billing codes assigned:** Transfer form → Billing code verification → OTP verification → Transfer pending

---

## 📊 **Current Progress Status**

### ✅ **COMPLETED TASKS**

#### 1. **Database Schema Design & Implementation** ✅
- **Tables Created:**
  ```sql
  ✅ billing_code_settings (6 basic settings)
  ✅ user_billing_codes (per-user assignments)
  ✅ wire_transfer_fields (field management)
  ```
- **Key Features:**
  - Simple per-user assignment (0-4 codes)
  - Custom popup descriptions for user guidance
  - No complex global requirements

#### 2. **Admin Menu Structure Enhancement** ✅
- **Added 3 Menu Items:**
  - 🔧 **Billing Code Settings** - User assignment & security levels
  - ✏️ **Edit Billing Code** - Individual code editing
  - 📋 **Wire Transfer Fields** - Field management

#### 3. **Simple Billing Code Assignment Page** ✅
- **Features Working:**
  - User search/selection dropdown
  - Per-user billing code assignment (1-4 codes)
  - Custom names, codes, and descriptions
  - Individual code activation/deactivation
  - Clear all codes functionality

### 🔄 **REWORKED: Billing Code System Settings Page**

#### **NEW PURPOSE:** User Security Level Assignment
The "Billing Code Settings" page has been completely reworked to be the main assignment interface:

**Features:**
- **User Selection:** Dropdown with all active users
- **Security Level Assignment:**
  - ○ OTP Only (current system)
  - ○ 1 Billing Code + OTP
  - ○ 2 Billing Codes + OTP  
  - ○ 3 Billing Codes + OTP
  - ○ 4 Billing Codes + OTP
  - ○ Custom 1 Configuration (Tax + COT Codes)
  - ○ Custom 2 Configuration (Tax + IMF + Security PIN)
  - ○ Custom 3 Configuration (IMF + COT + Security PIN)
- **Popup Configuration:** Global title and subtitle settings
- **Auto-Assignment:** Selecting security level creates default codes

---

## 🗄️ **Database Structure**

### **billing_code_settings Table**
```sql
- billing_verification_enabled: 'true'/'false'
- max_billing_attempts: 3
- billing_code_timeout: 300 (seconds)
- billing_popup_title: 'Billing Code Verification'
- billing_popup_subtitle: 'Please enter your billing code to continue'
```

### **user_billing_codes Table**
```sql
- user_id: Foreign key to accounts table
- billing_position: 1-4 (position in verification sequence)
- billing_name: 'Tax Code Required', 'IMF Code Required', etc.
- billing_code: Actual verification code
- billing_description: Custom popup text for user guidance
- is_active: 1/0 (individual code activation)
```

### **Default Code Types**
- **Position 1:** Tax Code Required
- **Position 2:** IMF Code Required  
- **Position 3:** COT Code Required
- **Position 4:** Security PIN Required

---

## 🎨 **Transfer Flow with Popup**

### **Popup Structure (NEEDS IMPLEMENTATION)**
```
┌─────────────────────────────────────┐
│        Billing Code Verification    │
│                                     │
│  Tax Code Required                  │
│  _________________________________ │
│                                     │
│  Contact SecureBank for your tax    │
│  verification code to proceed       │
│                                     │
│  [Progress Bar with Loading]        │
│                                     │
│     [Cancel]    [Verify Code]       │
└─────────────────────────────────────┘
```

### **Key Requirements:**
- **Dynamic Content:** Code label from `billing_name`, description from `billing_description`
- **Loading Progress Bar:** Must include loading animation
- **Site Name Integration:** Use dynamic site name in descriptions
- **Multiple Codes:** If user has multiple codes, show them sequentially
- **Error Handling:** Invalid code attempts with timeout protection

---

## 📋 **PENDING TASKS**

### 🔴 **HIGH PRIORITY - NEXT STEPS**

#### 1. **Billing Code Verification Popup Implementation**
- Create modal popup component with loading progress bar
- Implement dynamic content loading from database
- Add sequential verification for multiple codes
- Include proper error handling and timeout protection

#### 2. **Wire Transfer Form Enhancement**
- Modify existing transfer form for wire transfer fields
- Add billing code verification step before OTP
- Implement proper form validation and user feedback

#### 3. **Transfer Processing Logic**
- Intercept wire transfer submissions
- Check if user has billing codes assigned
- Route to billing verification or OTP accordingly
- Set transfer status to 'pending' for billing code users

### 🟡 **MEDIUM PRIORITY**

#### 4. **Wire Transfer Fields Management Page**
- Create admin interface for enabling/disabling form fields
- Include SWIFT codes, routing numbers, etc.
- Country-adaptable field configurations

#### 5. **Admin Transfer Management Enhancement**
- Enhance admin/transfers.php for wire transfer editing
- Add status management (pending→processing→completed)
- Include wire transfer specific data display

### 🟢 **LOW PRIORITY**

#### 6. **Settings & Configuration Management**
- Implement caching for billing code settings
- Add real-time updates and session management
- Integration with existing super_admin_settings

#### 7. **Testing & Quality Assurance**
- Comprehensive testing of entire system
- Security validation and edge case handling
- Ensure no breaking changes to existing transfers

---

## 🔧 **Technical Implementation Notes**

### **File Structure**
```
admin/
├── billing-code-settings.php (✅ REWORKED - Main assignment page)
├── edit-billing-code.php (✅ COMPLETED - Individual editing)
├── wire-transfer-fields.php (❌ PENDING)
└── includes/admin-sidebar.php (✅ UPDATED)

user/
├── transfer/ (❌ NEEDS ENHANCEMENT)
└── dashboard/ (✅ COMPLETED - Other fixes)

database/
├── billing_code_settings (✅ CREATED)
├── user_billing_codes (✅ CREATED)
└── wire_transfer_fields (✅ CREATED)
```

### **Key Functions Needed**
```php
// Check if user has billing codes
function userHasBillingCodes($user_id) { }

// Get user's active billing codes
function getUserBillingCodes($user_id) { }

// Verify billing code
function verifyBillingCode($user_id, $position, $code) { }

// Create billing code popup content
function getBillingCodePopupContent($user_id, $position) { }
```

---

## 🚨 **CRITICAL REQUIREMENTS**

### **Popup Implementation Must Include:**
1. **Loading Progress Bar** - Visual feedback during verification
2. **Dynamic Content** - Code names and descriptions from database
3. **Site Name Integration** - Use actual site name in descriptions
4. **Sequential Flow** - Handle multiple codes in order
5. **Error Handling** - Proper timeout and attempt limiting
6. **Mobile Responsive** - Work on all screen sizes

### **Security Requirements:**
- Maximum 3 attempts per billing code
- 5-minute timeout protection
- Complete audit logging
- Case-sensitive code verification
- Session management for verification state

### **User Experience:**
- Clear progress indicators
- Helpful error messages
- Smooth transitions between steps
- Consistent with existing OTP flow
- Professional banking appearance

---

## 💡 **Next AI Instructions**

### **Immediate Focus:**
1. **Implement the billing code verification popup** with loading progress bar
2. **Enhance the wire transfer form** to include billing code step
3. **Test the complete flow** from form submission to transfer pending

### **Key Files to Work With:**
- `user/transfer/` - Main transfer forms
- `admin/billing-code-settings.php` - Already reworked and functional
- `admin/edit-billing-code.php` - Individual code editing (completed)

### **Database is Ready:**
- All tables created and functional
- Sample data can be inserted for testing
- Settings system is operational

### **Testing Approach:**
1. Assign billing codes to a test user
2. Attempt wire transfer as that user
3. Verify popup appears with correct content
4. Test complete verification flow
5. Ensure OTP-only users are unaffected

---

## 🎯 **Success Criteria**

The system will be considered complete when:
- ✅ Admin can assign security levels to users
- ✅ Users with no codes proceed with OTP only (unchanged)
- ❌ Users with codes see billing verification popup first
- ❌ Popup shows custom descriptions and loading progress
- ❌ After billing verification, users proceed to OTP
- ❌ Transfers go to 'pending' status for billing code users
- ❌ Admin can manage pending wire transfers

**Current Status: ~40% Complete - Core infrastructure ready, popup implementation needed**
